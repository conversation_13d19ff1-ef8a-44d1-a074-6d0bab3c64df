import { Op } from 'sequelize';
import { responseModel } from '../../models';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

interface ResponseFilters {
  timeFilter?: string;
  customStartDate?: string;
  customEndDate?: string;
  page?: number;
  limit?: number;
}

/**
 * Data Access Layer function to retrieve survey responses
 *
 * This function fetches responses for a specific survey with filtering and pagination support.
 * Used for displaying responses on the dashboard.
 *
 * Security features:
 * - Validates account ownership by including account_id in query
 * - Comprehensive error handling with logging
 *
 * Performance considerations:
 * - Orders by creation date (newest first) for better UX
 * - Supports pagination to handle large datasets
 * - Uses indexed fields (survey_id, account_id) for optimal query performance
 *
 * @param surveyId - The unique UUID of the survey whose responses to retrieve
 * @param accountId - The unique UUID of the account that owns the survey
 * @param filters - Optional filters for responses
 * @returns Promise<{success: boolean, message: string, payload: any}> - Operation result with responses
 *
 * @example
 * const result = await readResponses(surveyId, accountId, { page: 1, limit: 10 });
 * if (result.success) {
 *   console.log(`Found ${result.payload.responses.length} responses`);
 * }
 */
export const readResponses: any = async (surveyId: string, accountId: string, filters: ResponseFilters = {}) => {
  try {
    const { timeFilter, customStartDate, customEndDate, page = 1, limit = 10 } = filters;

    // Build where conditions
    const whereConditions: any = {
      survey_id: surveyId,
      account_id: accountId, // Security: Ensure account owns the survey
      is_deleted: false, // Only show non-deleted responses
    };

    // Apply time filter if provided
    if (timeFilter && timeFilter !== 'all-time') {
      if (timeFilter === 'custom-range' && customStartDate && customEndDate) {
        // Handle custom date range
        const startDate = new Date(customStartDate);
        const endDate = new Date(customEndDate);
        // Set end date to end of day
        endDate.setHours(23, 59, 59, 999);

        whereConditions.created_at = {
          [Op.between]: [startDate, endDate],
        };
      } else {
        // Handle predefined time filters
        const now = new Date();
        let startDate: Date;

        switch (timeFilter) {
          case 'last-24-hours':
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
          case '7-days':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30-days':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '90-days':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(0); // Default to all time
        }

        whereConditions.created_at = {
          [Op.gte]: startDate,
        };
      }
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await responseModel.count({
      where: whereConditions,
    });

    // Fetch responses with pagination
    const responseData = await responseModel.findAll({
      where: whereConditions,
      order: [['created_at', 'DESC']], // Newest first
      limit,
      offset,
      attributes: ['response_data', 'respondent_details', 'meta', 'created_at', 'is_deleted', 'delete_reason'],
    });

    // Transform responses to array format
    const responses: any[] = responseData.map(responseData => {
      // Clean meta data - remove sensitive information
      const cleanMeta = responseData.dataValues.meta
        ? {
            ...responseData.dataValues.meta,
            // Remove any sensitive fields if they exist
            ip: undefined,
            // Keep userAgent for debugging and analytics purposes
          }
        : {};

      return {
        // Exclude response ID for privacy
        response_data: responseData.dataValues.response_data,
        respondent_details: responseData.dataValues.respondent_details,
        meta: cleanMeta,
        created_at: responseData.dataValues.created_at,
        is_deleted: responseData.dataValues.is_deleted,
        delete_reason: responseData.dataValues.delete_reason,
      };
    });

    // Generate analytics data
    const analytics = await generateAnalytics(surveyId, accountId, timeFilter, customStartDate, customEndDate);

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses retrieved`,
      payload: {
        responses,
        totalCount,
        totalResponses: analytics.totalResponses,
        avgCompletionTime: analytics.avgCompletionTime,
        responseTimestamps: analytics.responseTimestamps,
        responsesByDay: analytics.responsesByDay,
      },
    };
  } catch (error) {
    // Log error for debugging and monitoring
    logger.error('Error retrieving responses:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve responses`,
      payload: error,
    };
  }
};

/**
 * Generate analytics data for survey responses
 *
 * @param surveyId - Survey ID to generate analytics for
 * @param accountId - Account ID for security validation
 * @param timeFilter - Optional time filter
 * @param customStartDate - Optional custom start date
 * @param customEndDate - Optional custom end date
 * @returns Analytics object with response metrics
 */
const generateAnalytics = async (surveyId: string, accountId: string, timeFilter?: string, customStartDate?: string, customEndDate?: string) => {
  try {
    // Build where conditions for analytics
    const whereConditions: any = {
      survey_id: surveyId,
      account_id: accountId,
      is_deleted: false,
    };

    // Apply time filter if provided
    if (timeFilter && timeFilter !== 'all-time') {
      if (timeFilter === 'custom-range' && customStartDate && customEndDate) {
        // Handle custom date range
        const startDate = new Date(customStartDate);
        const endDate = new Date(customEndDate);
        // Set end date to end of day
        endDate.setHours(23, 59, 59, 999);

        whereConditions.created_at = {
          [Op.between]: [startDate, endDate],
        };
      } else {
        // Handle predefined time filters
        const now = new Date();
        let startDate: Date;

        switch (timeFilter) {
          case 'last-24-hours':
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
          case '7-days':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30-days':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '90-days':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(0);
        }

        whereConditions.created_at = {
          [Op.gte]: startDate,
        };
      }
    }

    // Get all responses for analytics
    const allResponses = await responseModel.findAll({
      where: whereConditions,
      attributes: ['created_at', 'meta'],
      order: [['created_at', 'ASC']],
    });

    const totalResponses = allResponses.length;
    let avgCompletionTime = 0;
    const responsesByDay: any = {};
    const responseTimestamps: string[] = [];

    allResponses.forEach(response => {
      // Calculate completion time if available in meta
      if (response.dataValues.meta?.completionTime) {
        avgCompletionTime += response.dataValues.meta.completionTime;
      }

      // Group by day for chart data
      const dayKey = response.dataValues.created_at.toISOString().split('T')[0];
      responsesByDay[dayKey] = (responsesByDay[dayKey] || 0) + 1;

      // Add timestamp to array (always use full ISO string for array)
      responseTimestamps.push(response.dataValues.created_at.toISOString());
    });

    // Calculate average completion time
    if (totalResponses > 0 && avgCompletionTime > 0) {
      avgCompletionTime = Math.round(avgCompletionTime / totalResponses);
    }

    return {
      totalResponses,
      avgCompletionTime,
      responsesByDay,
      responseTimestamps,
    };
  } catch (error) {
    logger.error('Error generating analytics:', error);
    return {
      totalResponses: 0,
      avgCompletionTime: 0,
      responsesByDay: {},
      responseTimestamps: [],
    };
  }
};
